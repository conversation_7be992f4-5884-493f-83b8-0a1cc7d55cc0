<?php
/**
 * Test script to verify duplicate slip detection in user top-up history
 */

require_once 'config/db_config.php';
require_once 'classes/PaymentManager.php';

try {
    echo "Testing duplicate slip detection in user top-up history...\n\n";
    
    $paymentManager = new PaymentManager();
    
    // Test with a user that has top-ups (try different user IDs)
    $test_users = [1, 2, 3, 4, 5];

    foreach ($test_users as $user_id) {
        $topups = $paymentManager->getUserTopUps($user_id);

        if (count($topups) > 0) {
            echo "User ID: $user_id\n";
            echo "Total top-ups: " . count($topups) . "\n\n";

            foreach ($topups as $topup) {
                echo "Topup ID: {$topup['id']}\n";
                echo "Amount: {$topup['amount']} THB\n";
                echo "Status: {$topup['status']}\n";
                echo "Created: {$topup['created_at']}\n";

                if ($topup['trans_id']) {
                    echo "Transaction ID: {$topup['trans_id']}\n";
                    echo "Is Duplicate Slip: " . ($topup['is_duplicate_slip'] ? 'YES' : 'NO') . "\n";
                } else {
                    echo "Transaction ID: Not available\n";
                    echo "Is Duplicate Slip: N/A\n";
                }

                echo "---\n";
            }
            break; // Found a user with top-ups, stop testing
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
