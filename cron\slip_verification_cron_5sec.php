<?php
/**
 * Slip Verification Cron Job - ทุก 5 วินาที
 * ทำงานเหมือนหน้า slip verification แต่รันอัตโนมัติ
 */

// ป้องกันการเรียกจาก web browser
if (php_sapi_name() !== 'cli' && !isset($_GET['force'])) {
    http_response_code(403);
    die('Access denied. This script can only be run from command line.');
}

// เปลี่ยน working directory ไปยัง root ของโปรเจค
$script_dir = dirname(__FILE__);
$project_root = dirname($script_dir);
chdir($project_root);

// Include required files
require_once 'config/db_config.php';
require_once 'classes/SlipVerificationManager.php';

// สร้าง log file
$log_file = $script_dir . '/logs/slip_verification_5sec.log';
$log_dir = dirname($log_file);

if (!is_dir($log_dir)) {
    mkdir($log_dir, 0755, true);
}

function writeLog($message, $type = 'INFO') {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[{$timestamp}] [{$type}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX);
    
    // แสดงใน console ด้วย
    echo $log_message;
}

// ป้องกันการรันซ้ำ
$lock_file = $script_dir . '/slip_verification.lock';
if (file_exists($lock_file)) {
    $lock_time = filemtime($lock_file);
    if (time() - $lock_time < 10) { // ถ้ารันไปแล้วไม่เกิน 10 วินาที
        writeLog("Script is already running, skipping...", "SKIP");
        exit(0);
    }
}
file_put_contents($lock_file, time());

try {
    writeLog("=== เริ่มการตรวจสอบสลิปอัตโนมัติ ===", "START");
    
    // เชื่อมต่อฐานข้อมูล
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("ไม่สามารถเชื่อมต่อฐานข้อมูลได้");
    }
    
    writeLog("✅ เชื่อมต่อฐานข้อมูลสำเร็จ", "SUCCESS");
    
    // สร้าง SlipVerificationManager
    $slipManager = new SlipVerificationManager();
    writeLog("✅ สร้าง SlipVerificationManager สำเร็จ", "SUCCESS");
    
    // ดึงสถิติก่อนการตรวจสอบ
    $query = "SELECT COUNT(*) as pending_count FROM top_ups WHERE status = 'pending'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $before_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $pending_before = $before_stats['pending_count'];
    
    writeLog("📊 การเติมเงินที่รอการอนุมัติ: {$pending_before} รายการ", "INFO");
    
    // ตรวจสอบและอนุมัติอัตโนมัติ
    $result = $slipManager->processAutoApproval();
    
    if ($result['success']) {
        $processed = $result['processed'];
        $approved = $result['approved'];
        
        writeLog("✅ การตรวจสอบเสร็จสิ้น: ประมวลผล {$processed} รายการ, อนุมัติ {$approved} รายการ", "SUCCESS");
        
        // แสดงรายละเอียดการอนุมัติ
        if ($approved > 0 && !empty($result['results'])) {
            writeLog("💰 รายการที่อนุมัติสำเร็จ:", "SUCCESS");
            foreach ($result['results'] as $res) {
                if ($res['approved']) {
                    writeLog("   - Trans ID: {$res['trans_id']}, จำนวน: {$res['amount']} ฿", "SUCCESS");
                }
            }
        }
        
        // แสดงรายละเอียดที่ไม่สามารถอนุมัติได้ (เฉพาะเมื่อมีปัญหาสำคัญ)
        if ($processed > $approved && !empty($result['results'])) {
            $error_count = 0;
            $no_match_count = 0;
            
            foreach ($result['results'] as $res) {
                if (!$res['approved']) {
                    if (strpos($res['reason'], 'No matching topup') !== false) {
                        $no_match_count++;
                    } elseif (strpos($res['reason'], 'Duplicate slip') !== false) {
                        // แสดงข้อมูลสลิปซ้ำแยกต่างหาก
                        writeLog("🔄 สลิปซ้ำ: Trans ID {$res['trans_id']}, {$res['reason']}", "INFO");
                    } else {
                        $error_count++;
                        if ($error_count <= 2) { // แสดงเฉพาะ error สำคัญ 2 รายการแรก
                            writeLog("⚠️ ข้อผิดพลาด: Trans ID {$res['trans_id']}, เหตุผล: {$res['reason']}", "WARNING");
                        }
                    }
                }
            }
            
            if ($no_match_count > 0) {
                writeLog("ℹ️ ธุรกรรมที่ไม่พบการจับคู่: {$no_match_count} รายการ", "INFO");
            }
            
            if ($error_count > 2) {
                writeLog("⚠️ และมีข้อผิดพลาดอื่นๆ อีก " . ($error_count - 2) . " รายการ", "WARNING");
            }
        }
        
        // ดึงสถิติหลังการตรวจสอบ
        $stmt->execute();
        $after_stats = $stmt->fetch(PDO::FETCH_ASSOC);
        $pending_after = $after_stats['pending_count'];
        
        if ($pending_before != $pending_after) {
            writeLog("📈 การเติมเงินที่รอการอนุมัติลดลง: {$pending_before} → {$pending_after} รายการ", "INFO");
        }
        
    } else {
        writeLog("❌ การตรวจสอบล้มเหลว: " . $result['message'], "ERROR");
    }
    
    // ดึงสถิติการอนุมัติวันนี้
    $query = "SELECT COUNT(*) as approved_today FROM top_ups 
              WHERE status = 'approved' 
              AND DATE(approved_at) = CURDATE()";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $today_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $approved_today = $today_stats['approved_today'];
    
    writeLog("📊 สถิติวันนี้: อนุมัติแล้ว {$approved_today} รายการ", "INFO");
    
    writeLog("=== สิ้นสุดการตรวจสอบ ===", "END");
    
} catch (Exception $e) {
    writeLog("💥 เกิดข้อผิดพลาด: " . $e->getMessage(), "ERROR");
    writeLog("📍 ไฟล์: " . $e->getFile() . " บรรทัด: " . $e->getLine(), "ERROR");
    
    // ส่งอีเมลแจ้งเตือน (ถ้าต้องการ)
    // mail('<EMAIL>', 'Slip Verification Error', $e->getMessage());
} finally {
    // ลบ lock file
    if (file_exists($lock_file)) {
        unlink($lock_file);
    }
}

// ทำความสะอาด log เก่า (เก็บไว้ 3 วัน)
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    
    // เก็บเฉพาะ log ล่าสุด 2000 บรรทัด
    if (count($lines) > 2000) {
        $recent_lines = array_slice($lines, -2000);
        file_put_contents($log_file, implode("\n", $recent_lines));
        writeLog("🧹 ทำความสะอาด log เก่า", "MAINTENANCE");
    }
}

?>
