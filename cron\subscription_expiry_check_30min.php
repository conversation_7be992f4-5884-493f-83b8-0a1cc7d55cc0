<?php
/**
 * Cron Job: Check subscription expiry every 30 minutes
 * ตรวจสอบการหมดอายุของ subscription ทุก 30 นาที
 */

// Set the working directory to the project root
$project_root = dirname(__DIR__);
chdir($project_root);

require_once 'config/db_config.php';
require_once 'classes/SubscriptionManager.php';

// Log file for this specific cron job
$log_file = 'cron/logs/subscription_expiry_30min.log';

// Create logs directory if it doesn't exist
if (!file_exists('cron/logs')) {
    mkdir('cron/logs', 0755, true);
}

function writeLog($message, $level = "INFO") {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry;
}

function cleanOldLogs() {
    global $log_file;
    if (file_exists($log_file)) {
        $lines = file($log_file);
        if (count($lines) > 1000) {
            // Keep only last 500 lines
            $lines = array_slice($lines, -500);
            file_put_contents($log_file, implode('', $lines));
            writeLog("🧹 Cleaned old log entries", "MAINTENANCE");
        }
    }
}

try {
    writeLog("=== Starting subscription expiry check (30min interval) ===", "START");
    
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception("Failed to connect to database");
    }
    
    writeLog("✅ Database connection successful", "SUCCESS");
    
    $subscriptionManager = new SubscriptionManager();
    writeLog("✅ SubscriptionManager created successfully", "SUCCESS");
    
    // Get current subscription statistics
    $query = "SELECT 
                COUNT(CASE WHEN status = 'active' AND end_date >= CURDATE() THEN 1 END) as active_count,
                COUNT(CASE WHEN status = 'active' AND end_date < CURDATE() THEN 1 END) as expired_active_count,
                COUNT(CASE WHEN status = 'expired' THEN 1 END) as already_expired_count
              FROM user_subscriptions";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    writeLog("📊 Current subscription status:", "INFO");
    writeLog("   - Active (valid): {$stats['active_count']}", "INFO");
    writeLog("   - Active (expired): {$stats['expired_active_count']}", "INFO");
    writeLog("   - Already marked expired: {$stats['already_expired_count']}", "INFO");
    
    if ($stats['expired_active_count'] > 0) {
        writeLog("🔍 Found {$stats['expired_active_count']} subscriptions that need to be expired", "INFO");
        
        // Check for expired subscriptions
        $expired_result = $subscriptionManager->checkExpiredSubscriptions();
        
        if ($expired_result['success']) {
            writeLog("✅ Subscription expiry check completed: processed {$expired_result['processed']} subscriptions", "SUCCESS");
            
            if ($expired_result['processed'] > 0) {
                foreach ($expired_result['results'] as $result) {
                    if ($result['result']['success']) {
                        writeLog("   ✅ Successfully expired subscription for user: {$result['username']} (ID: {$result['user_id']})", "SUCCESS");
                    } else {
                        writeLog("   ❌ Failed to expire subscription for user: {$result['username']} (ID: {$result['user_id']}) - {$result['result']['message']}", "ERROR");
                    }
                }
                
                // Update statistics after processing
                $stmt->execute();
                $new_stats = $stmt->fetch(PDO::FETCH_ASSOC);
                writeLog("📊 Updated statistics:", "INFO");
                writeLog("   - Active (valid): {$new_stats['active_count']}", "INFO");
                writeLog("   - Active (expired): {$new_stats['expired_active_count']}", "INFO");
                writeLog("   - Already marked expired: {$new_stats['already_expired_count']}", "INFO");
            }
        } else {
            writeLog("❌ Error checking expired subscriptions: " . $expired_result['message'], "ERROR");
        }
    } else {
        writeLog("ℹ️ No expired subscriptions found - all subscriptions are up to date", "INFO");
    }
    
    // Check for renewed subscriptions (users who renewed but accounts still disabled)
    writeLog("🔍 Checking for renewed subscriptions...", "INFO");
    $renewed_result = $subscriptionManager->checkRenewedSubscriptions();
    
    if ($renewed_result['success']) {
        if ($renewed_result['processed'] > 0) {
            writeLog("✅ Renewed subscription check completed: processed {$renewed_result['processed']} subscriptions", "SUCCESS");
            
            foreach ($renewed_result['results'] as $result) {
                if ($result['result']['success']) {
                    writeLog("   ✅ Successfully re-enabled subscription for user: {$result['username']} (ID: {$result['user_id']})", "SUCCESS");
                } else {
                    writeLog("   ❌ Failed to re-enable subscription for user: {$result['username']} (ID: {$result['user_id']}) - {$result['result']['message']}", "ERROR");
                }
            }
        } else {
            writeLog("ℹ️ No renewed subscriptions found", "INFO");
        }
    } else {
        writeLog("❌ Error checking renewed subscriptions: " . $renewed_result['message'], "ERROR");
    }
    
    writeLog("=== Subscription expiry check completed successfully ===", "END");
    
    // Clean old logs
    cleanOldLogs();
    
} catch (Exception $e) {
    writeLog("❌ Fatal error in subscription expiry check: " . $e->getMessage(), "ERROR");
    writeLog("Stack trace: " . $e->getTraceAsString(), "ERROR");
    exit(1);
}
?>
