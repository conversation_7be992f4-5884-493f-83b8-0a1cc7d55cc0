<?php
require_once 'classes/SubscriptionManager.php';
require_once 'classes/PaymentManager.php';
require_once 'includes/helpers.php';

$subscriptionManager = new SubscriptionManager();
$paymentManager = new PaymentManager();

// Get user's active subscription
$active_subscription = $subscriptionManager->getUserActiveSubscription($user['id']);

// Get user's recent top-ups
$recent_topups = $paymentManager->getUserTopUps($user['id']);
$recent_topups = array_slice($recent_topups, 0, 5); // Show only last 5

// Get user's media server accounts
$database = new Database();
$conn = $database->getConnection();

$query = "SELECT * FROM emby_accounts WHERE user_id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$emby_account = $stmt->fetch(PDO::FETCH_ASSOC);

$query = "SELECT * FROM jellyfin_accounts WHERE user_id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $user['id']);
$stmt->execute();
$jellyfin_account = $stmt->fetch(PDO::FETCH_ASSOC);
?>

<div class="container py-4">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold text-gradient">สวัสดี, <?php echo htmlspecialchars($user['full_name']); ?>!</h2>
            <p class="text-muted text-shadow">ยินดีต้อนรับสู่แดชบอร์ดของคุณ</p>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3" data-animate="slide-in-up" style="animation-delay: 0.1s;">
            <div class="card bg-primary text-white card-hover">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">ยอดเงินคงเหลือ</h6>
                            <h4 class="mb-0 counter"><?php
                                $balance = floatval($user['balance']);
                                echo number_format($balance, 0, '.', ',') . ' ฿';
                            ?></h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wallet fa-2x opacity-75 pulse"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3" data-animate="slide-in-up" style="animation-delay: 0.2s;">
            <div class="card bg-success text-white card-hover">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">สถานะแพ็คเกจ</h6>
                            <h6 class="mb-0">
                                <?php if($active_subscription): ?>
                                    <i class="fas fa-check-circle me-1 bounce-in"></i>ใช้งานได้
                                <?php else: ?>
                                    <i class="fas fa-times-circle me-1 shake"></i>ไม่มีแพ็คเกจ
                                <?php endif; ?>
                            </h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-box fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3" data-animate="slide-in-up" style="animation-delay: 0.3s;">
            <div class="card bg-info text-white card-hover">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Emby Account</h6>
                            <h6 class="mb-0">
                                <?php if($emby_account): ?>
                                    <i class="fas fa-check-circle me-1"></i><?php echo $emby_account['status']; ?>
                                <?php else: ?>
                                    <i class="fas fa-times-circle me-1"></i>ไม่มี
                                <?php endif; ?>
                            </h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tv fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3" data-animate="slide-in-up" style="animation-delay: 0.4s;">
            <div class="card bg-warning text-white card-hover">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Jellyfin Account</h6>
                            <h6 class="mb-0">
                                <?php if($jellyfin_account): ?>
                                    <i class="fas fa-check-circle me-1"></i><?php echo $jellyfin_account['status']; ?>
                                <?php else: ?>
                                    <i class="fas fa-times-circle me-1"></i>ไม่มี
                                <?php endif; ?>
                            </h6>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-play fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Current Subscription -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-box me-2"></i>แพ็คเกจปัจจุบัน</h5>
                    <a href="?page=packages" class="btn btn-sm btn-outline-primary">ดูแพ็คเกจทั้งหมด</a>
                </div>
                <div class="card-body">
                    <?php if($active_subscription): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary"><?php echo htmlspecialchars($active_subscription['package_name']); ?></h6>
                                <p class="text-muted mb-2"><?php echo htmlspecialchars($active_subscription['description']); ?></p>
                                <p class="mb-1"><strong>วันที่เริ่ม:</strong> <?php echo date('d/m/Y', strtotime($active_subscription['start_date'])); ?></p>
                                <p class="mb-1"><strong>วันหมดอายุ:</strong> <?php echo date('d/m/Y', strtotime($active_subscription['end_date'])); ?></p>
                                
                                <?php
                                $days_left = ceil((strtotime($active_subscription['end_date']) - time()) / (60 * 60 * 24));
                                ?>
                                <p class="mb-0">
                                    <strong>เหลือเวลา:</strong> 
                                    <span class="badge <?php echo $days_left > 7 ? 'bg-success' : ($days_left > 3 ? 'bg-warning' : 'bg-danger'); ?>">
                                        <?php echo $days_left; ?> วัน
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <div class="progress mb-2">
                                    <?php
                                    $total_days = ceil((strtotime($active_subscription['end_date']) - strtotime($active_subscription['start_date'])) / (60 * 60 * 24));
                                    $used_days = $total_days - $days_left;
                                    $progress = ($used_days / $total_days) * 100;
                                    ?>
                                    <div class="progress-bar" role="progressbar" style="width: <?php echo $progress; ?>%"></div>
                                </div>
                                <small class="text-muted">ใช้งานไปแล้ว <?php echo $used_days; ?> จาก <?php echo $total_days; ?> วัน</small>
                            </div>
                        </div>
                        
                        <?php if($days_left <= 7): ?>
                        <div class="alert alert-warning mt-3 mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            แพ็คเกจของคุณจะหมดอายุในอีก <?php echo $days_left; ?> วัน กรุณาต่ออายุเพื่อใช้งานต่อ
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-box-open text-muted" style="font-size: 3rem;"></i>
                            <h6 class="mt-3 text-muted">คุณยังไม่มีแพ็คเกจที่ใช้งานอยู่</h6>
                            <p class="text-muted">เลือกซื้อแพ็คเกจเพื่อเริ่มใช้งาน Media Server</p>
                            <a href="?page=packages" class="btn btn-primary">เลือกแพ็คเกจ</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Media Server Accounts -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-server me-2"></i>บัญชี Media Server</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="border rounded p-3 mb-3 mb-md-0">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0"><i class="fas fa-tv me-2 text-info"></i>Emby</h6>
                                    <?php
                                    // Check Emby server status
                                    $emby_server_status = 'offline';
                                    $emby_server_class = 'bg-danger';

                                    if($emby_account) {
                                        require_once 'classes/EmbyAPI.php';
                                        $database = new Database();
                                        $conn = $database->getConnection();
                                        $settings = new SystemSettings($conn);

                                        $emby_api = new EmbyAPI(
                                            $settings->get('emby_server_url'),
                                            $settings->get('emby_api_key')
                                        );

                                        if($emby_api->testConnection()) {
                                            $emby_server_status = 'online';
                                            $emby_server_class = 'bg-success';
                                        }
                                    }
                                    ?>
                                    <span class="badge <?php echo $emby_server_class; ?>">
                                        <?php echo $emby_server_status; ?>
                                    </span>
                                </div>
                                
                                <?php if($emby_account): ?>
                                    <p class="mb-1"><strong>Username:</strong> <?php echo htmlspecialchars($emby_account['emby_username']); ?></p>
                                    <p class="mb-1"><strong>Password:</strong>
                                        <span class="text-success">
                                            <span id="emby-password-hidden">••••••••</span>
                                            <span id="emby-password-shown" style="display: none;"><?php echo htmlspecialchars($emby_account['emby_password']); ?></span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="togglePassword('emby')">
                                                <i class="fas fa-eye" id="emby-eye-icon"></i>
                                            </button>
                                        </span>
                                    </p>
                                    <p class="mb-1"><strong>Server:</strong> <?php echo htmlspecialchars($emby_account['server_url']); ?></p>
                                    <?php if($emby_account['status'] == 'active' && $active_subscription): ?>
                                        <a href="<?php echo htmlspecialchars($emby_account['server_url']); ?>" target="_blank" class="btn btn-sm btn-info">
                                            <i class="fas fa-external-link-alt me-1"></i>เข้าใช้งาน
                                        </a>
                                    <?php else: ?>
                                        <small class="text-muted">บัญชีถูกระงับ กรุณาต่ออายุแพ็คเกจ</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <p class="text-muted mb-0">ยังไม่มีบัญชี Emby</p>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="border rounded p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0"><i class="fas fa-play me-2 text-warning"></i>Jellyfin</h6>
                                    <?php
                                    // Check Jellyfin server status
                                    $jellyfin_server_status = 'offline';
                                    $jellyfin_server_class = 'bg-danger';

                                    if($jellyfin_account) {
                                        require_once 'classes/JellyfinAPI.php';
                                        $database = new Database();
                                        $conn = $database->getConnection();
                                        $settings = new SystemSettings($conn);

                                        $jellyfin_api = new JellyfinAPI(
                                            $settings->get('jellyfin_server_url'),
                                            $settings->get('jellyfin_api_key')
                                        );

                                        if($jellyfin_api->testConnection()) {
                                            $jellyfin_server_status = 'online';
                                            $jellyfin_server_class = 'bg-success';
                                        }
                                    }
                                    ?>
                                    <span class="badge <?php echo $jellyfin_server_class; ?>">
                                        <?php echo $jellyfin_server_status; ?>
                                    </span>
                                </div>
                                
                                <?php if($jellyfin_account): ?>
                                    <p class="mb-1"><strong>Username:</strong> <?php echo htmlspecialchars($jellyfin_account['jellyfin_username']); ?></p>
                                    <p class="mb-1"><strong>Password:</strong>
                                        <span class="text-success">
                                            <span id="jellyfin-password-hidden">••••••••</span>
                                            <span id="jellyfin-password-shown" style="display: none;"><?php echo htmlspecialchars($jellyfin_account['jellyfin_password']); ?></span>
                                            <button type="button" class="btn btn-sm btn-outline-secondary ms-1" onclick="togglePassword('jellyfin')">
                                                <i class="fas fa-eye" id="jellyfin-eye-icon"></i>
                                            </button>
                                        </span>
                                    </p>
                                    <p class="mb-1"><strong>Server:</strong> <?php echo htmlspecialchars($jellyfin_account['server_url']); ?></p>
                                    <?php if($jellyfin_account['status'] == 'active' && $active_subscription): ?>
                                        <a href="<?php echo htmlspecialchars($jellyfin_account['server_url']); ?>" target="_blank" class="btn btn-sm btn-warning">
                                            <i class="fas fa-external-link-alt me-1"></i>เข้าใช้งาน
                                        </a>
                                    <?php else: ?>
                                        <small class="text-muted">บัญชีถูกระงับ กรุณาต่ออายุแพ็คเกจ</small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <p class="text-muted mb-0">ยังไม่มีบัญชี Jellyfin</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions & Recent Activity -->
        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>การดำเนินการด่วน</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="?page=topup" class="btn btn-success">
                            <i class="fas fa-plus-circle me-2"></i>เติมเงิน
                        </a>
                        <a href="?page=packages" class="btn btn-primary">
                            <i class="fas fa-shopping-cart me-2"></i>ซื้อแพ็คเกจ
                        </a>
                        <a href="?page=profile" class="btn btn-outline-secondary">
                            <i class="fas fa-user-edit me-2"></i>แก้ไขโปรไฟล์
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Recent Top-ups -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>การเติมเงินล่าสุด</h5>
                    <a href="?page=topup" class="btn btn-sm btn-outline-primary">ดูทั้งหมด</a>
                </div>
                <div class="card-body">
                    <?php if(!empty($recent_topups)): ?>
                        <?php foreach($recent_topups as $topup): ?>
                        <div class="d-flex justify-content-between align-items-center mb-2 pb-2 border-bottom">
                            <div>
                                <small class="text-muted"><?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?></small>
                                <div>
                                    <?php echo number_format($topup['amount'], 2); ?> ฿
                                    <?php if ($topup['status'] == 'approved' && $topup['is_duplicate_slip']): ?>
                                        <small class="text-warning ms-1" title="สลิปซ้ำ">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </small>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <span class="badge <?php
                                echo $topup['status'] == 'approved' ? 'bg-success' :
                                    ($topup['status'] == 'pending' ? 'bg-warning' : 'bg-danger');
                            ?>" <?php
                                if ($topup['status'] == 'rejected') {
                                    $slip_data = json_decode($topup['slip_data'], true);
                                    if ($slip_data && isset($slip_data['rejection_reason'])) {
                                        echo 'title="' . htmlspecialchars($slip_data['rejection_reason']) . '"';
                                    }
                                }
                            ?>>
                                <?php
                                if ($topup['status'] == 'rejected') {
                                    $slip_data = json_decode($topup['slip_data'], true);
                                    if ($slip_data && isset($slip_data['rejection_reason'])) {
                                        if (strpos($slip_data['rejection_reason'], 'สลิปซ้ำ') !== false) {
                                            echo 'สลิปซ้ำ - ปฏิเสธ';
                                        } elseif (strpos($slip_data['rejection_reason'], 'สลิปผิด') !== false) {
                                            echo 'สลิปผิด - ปฏิเสธ';
                                        } else {
                                            echo 'ปฏิเสธ';
                                        }
                                    } else {
                                        echo 'ปฏิเสธ';
                                    }
                                } else {
                                    echo $topup['status'] == 'approved' ? 'อนุมัติแล้ว' :
                                        ($topup['status'] == 'pending' ? 'รอดำเนินการ' : 'ปฏิเสธ');
                                }
                                ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="text-muted text-center mb-0">ยังไม่มีการเติมเงิน</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(type) {
    const hiddenElement = document.getElementById(type + '-password-hidden');
    const shownElement = document.getElementById(type + '-password-shown');
    const eyeIcon = document.getElementById(type + '-eye-icon');

    if (hiddenElement.style.display === 'none') {
        // Currently showing password, hide it
        hiddenElement.style.display = 'inline';
        shownElement.style.display = 'none';
        eyeIcon.className = 'fas fa-eye';
    } else {
        // Currently hiding password, show it
        hiddenElement.style.display = 'none';
        shownElement.style.display = 'inline';
        eyeIcon.className = 'fas fa-eye-slash';
    }
}
</script>
