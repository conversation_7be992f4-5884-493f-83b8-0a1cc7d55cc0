<?php
ob_start(); // Start output buffering
session_start();

// Set timezone to Thailand
date_default_timezone_set('Asia/Bangkok');

require_once 'config/db_config.php';
require_once 'includes/helpers.php';

// Get current page
$page = $_GET['page'] ?? 'home';

// Check if user is logged in
$is_logged_in = isset($_SESSION['user_id']);
$user = null;

if($is_logged_in) {
    $database = new Database();
    $conn = $database->getConnection();

    // Always fetch fresh user data from database to ensure balance is up-to-date
    $query = "SELECT * FROM users WHERE id = :user_id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->execute();

    if($stmt->rowCount() > 0) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        // Update session with fresh data
        $_SESSION['user_balance'] = $user['balance'];
    } else {
        // User not found, logout
        session_destroy();
        header('Location: index.php');
        exit;
    }
}

// Get system settings
$settings = new SystemSettings($conn ?? (new Database())->getConnection());
$site_name = $settings->get('site_name') ?? 'ดูหนังออนไลน์ 24hr.';
?>
<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $site_name; ?></title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><defs><linearGradient id='grad' x1='0%' y1='0%' x2='100%' y2='100%'><stop offset='0%' style='stop-color:%23007bff;stop-opacity:1' /><stop offset='100%' style='stop-color:%230056b3;stop-opacity:1' /></linearGradient></defs><rect width='100' height='100' rx='15' fill='url(%23grad)'/><circle cx='50' cy='50' r='25' fill='white'/><polygon points='45,35 45,65 70,50' fill='url(%23grad)'/></svg>">
    <link rel="shortcut icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><defs><linearGradient id='grad' x1='0%' y1='0%' x2='100%' y2='100%'><stop offset='0%' style='stop-color:%23007bff;stop-opacity:1' /><stop offset='100%' style='stop-color:%230056b3;stop-opacity:1' /></linearGradient></defs><rect width='100' height='100' rx='15' fill='url(%23grad)'/><circle cx='50' cy='50' r='25' fill='white'/><polygon points='45,35 45,65 70,50' fill='url(%23grad)'/></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/fonts.css" rel="stylesheet">
    <link href="assets/css/font-override.css" rel="stylesheet">
    <link href="assets/css/animations.css" rel="stylesheet">
    <style>
        /* Additional custom styles for enhanced appearance */
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .hero-section h1 {
            font-style: italic;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .card-title {
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-up {
            animation: slideInUp 0.5s ease-out;
        }

        @keyframes slideInUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .bounce-in {
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { transform: scale(1.05); }
            70% { transform: scale(0.9); }
            100% { transform: scale(1); opacity: 1; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Card Hover Effects */
        .card-hover {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Gradient Effects */
        .bg-gradient-dark {
            background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
        }

        /* Fix Dropdown Z-index */
        .navbar .dropdown-menu {
            z-index: 1050 !important;
            position: absolute !important;
        }

        .navbar-nav .dropdown-menu {
            z-index: 1050 !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            position: absolute !important;
        }

        .navbar-nav .dropdown {
            position: relative !important;
        }

        /* Ensure navbar has proper stacking context */
        .navbar {
            z-index: 1040 !important;
            position: relative !important;
        }

        /* Fix for Bootstrap dropdown positioning */
        .dropdown-menu.show {
            z-index: 1050 !important;
            display: block !important;
        }

        /* Additional fixes for content overlap */
        .hero-section {
            z-index: 1 !important;
            position: relative !important;
        }

        .container, .container-fluid {
            z-index: 1 !important;
            position: relative !important;
        }

        /* Ensure dropdown appears above all content */
        .navbar-nav .dropdown-menu {
            margin-top: 0 !important;
            border: 1px solid rgba(0,0,0,.15) !important;
            background-color: #fff !important;
        }

        /* Additional custom styles */
        .qr-code-container {
            text-align: center;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .qr-code-container:hover {
            border-color: #667eea;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Ripple effect for cards */
        .card {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: rippleEffect 0.6s linear;
            pointer-events: none;
        }

        @keyframes rippleEffect {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Enhanced navbar */
        .navbar {
            backdrop-filter: blur(10px);
            background: rgba(52, 58, 64, 0.95) !important;
        }

        /* Enhanced buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* Enhanced cards */
        .card {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
        }

        /* Enhanced form controls */
        .form-control {
            border-radius: var(--border-radius);
            border: 2px solid #e9ecef;
        }

        /* Enhanced badges */
        .badge {
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        /* Logo Container & Brand Styling */
        .logo-container {
            position: relative;
            display: inline-block;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0.95;
            filter: drop-shadow(0 4px 12px rgba(0,0,0,0.4));
            border: 2px solid rgba(255,255,255,0.1);
            position: relative;
            overflow: hidden;
        }

        .logo-icon i {
            font-size: 28px;
            color: #ffffff;
            transition: all 0.3s ease;
            z-index: 2;
            position: relative;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .logo-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%, rgba(0,0,0,0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .brand-text {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .brand-name {
            font-weight: 700;
            font-size: 1.4rem;
            color: #ffffff;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .brand-tagline {
            font-size: 0.75rem;
            color: rgba(255,255,255,0.7);
            font-weight: 400;
            margin-top: -2px;
            transition: all 0.3s ease;
        }

        .navbar-brand {
            padding: 0.75rem 0;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover .logo-icon {
            transform: scale(1.05) rotate(5deg);
            opacity: 1;
            filter: drop-shadow(0 6px 20px rgba(0,0,0,0.5));
            border-color: rgba(255,255,255,0.3);
            box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
        }

        .navbar-brand:hover .logo-icon::before {
            opacity: 1;
        }

        .navbar-brand:hover .logo-overlay {
            opacity: 1;
        }

        .navbar-brand:hover .brand-name {
            color: #f8f9fa;
            text-shadow: 0 3px 6px rgba(0,0,0,0.4);
            transform: translateX(2px);
        }

        .navbar-brand:hover .brand-tagline {
            color: rgba(255,255,255,0.9);
            transform: translateX(2px);
        }

        .navbar-brand:hover .logo-icon i {
            transform: scale(1.1);
            color: #ffffff;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .brand-tagline {
                display: none !important;
            }
        }

        @media (max-width: 768px) {
            .logo-icon {
                width: 45px;
                height: 45px;
                border-radius: 12px;
            }

            .logo-icon i {
                font-size: 22px;
            }

            .logo-overlay {
                border-radius: 12px;
            }

            .brand-name {
                font-size: 1.2rem;
            }

            .navbar-brand {
                padding: 0.5rem 0;
            }
        }

        /* Floating Contact Button */
        .floating-contact {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            animation: float 3s ease-in-out infinite;
        }

        .floating-contact .btn {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            transition: all 0.3s ease;
            background: linear-gradient(45deg, #00c851, #00ff41);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .floating-contact .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .floating-contact .btn:hover::before {
            left: 100%;
        }

        .floating-contact .btn:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 25px rgba(0, 200, 81, 0.4);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
            .floating-contact {
                bottom: 20px;
                right: 20px;
            }

            .floating-contact .btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body class="<?php echo ($page == 'home' || $page == '') ? 'homepage' : ''; ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <div class="logo-container me-3">
                    <div class="logo-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="logo-overlay"></div>
                </div>
                <div class="brand-text">
                    <span class="brand-name"><?php echo $site_name; ?></span>
                    <small class="brand-tagline d-none d-lg-block">Premium Streaming Service</small>
                </div>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page == 'home' ? 'active' : ''; ?>" href="index.php">หน้าแรก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page == 'contact' ? 'active' : ''; ?>" href="?page=contact">ติดต่อเรา</a>
                    </li>
                    <?php if($is_logged_in): ?>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page == 'dashboard' ? 'active' : ''; ?>" href="?page=dashboard">แดชบอร์ด</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page == 'packages' ? 'active' : ''; ?>" href="?page=packages">แพ็คเกจ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo $page == 'topup' ? 'active' : ''; ?>" href="?page=topup">เติมเงิน</a>
                    </li>
                    <?php if($user && $user['role'] == 'admin'): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            จัดการระบบ
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?page=admin_users">จัดการผู้ใช้</a></li>
                            <li><a class="dropdown-item" href="?page=admin_subscriptions">จัดการ Subscriptions</a></li>
                            <li><a class="dropdown-item" href="?page=admin_media_accounts">บัญชี Media Server</a></li>
                            <li><a class="dropdown-item" href="?page=admin_media">จัดการหนัง/ซีรีส์</a></li>
                            <li><a class="dropdown-item" href="?page=admin_topups">อนุมัติเติมเงิน</a></li>
                            <li><a class="dropdown-item" href="?page=slip_verification">ตรวจสอบสลิป</a></li>
                            <li><a class="dropdown-item" href="?page=admin_packages">จัดการแพ็คเกจ</a></li>
                            <li><a class="dropdown-item" href="?page=admin_settings">ตั้งค่าระบบ</a></li>

                        </ul>
                    </li>
                    <?php endif; ?>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if($is_logged_in): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?php echo htmlspecialchars($user['username']); ?>
                            <span class="badge bg-success ms-1"><?php
                                $balance = floatval($user['balance']);
                                echo number_format($balance, 0, '.', ',') . ' ฿';
                            ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="?page=profile">โปรไฟล์</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="?page=logout">ออกจากระบบ</a></li>
                        </ul>
                    </li>
                    <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=login">เข้าสู่ระบบ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="?page=register">สมัครสมาชิก</a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        <?php
        // Include the appropriate page
        switch($page) {
            case 'login':
                include 'pages/login.php';
                break;
            case 'register':
                include 'pages/register.php';
                break;
            case 'logout':
                include 'pages/logout.php';
                break;
            case 'contact':
                include 'pages/contact.php';
                break;
            case 'dashboard':
                if($is_logged_in) {
                    include 'pages/dashboard.php';
                } else {
                    header('Location: ?page=login');
                    exit;
                }
                break;
            case 'packages':
                if($is_logged_in) {
                    include 'pages/packages.php';
                } else {
                    header('Location: ?page=login');
                    exit;
                }
                break;
            case 'topup':
                if($is_logged_in) {
                    include 'pages/topup.php';
                } else {
                    header('Location: ?page=login');
                    exit;
                }
                break;
            case 'profile':
                if($is_logged_in) {
                    include 'pages/profile.php';
                } else {
                    header('Location: ?page=login');
                    exit;
                }
                break;
            // Admin pages
            case 'admin_users':
            case 'admin_subscriptions':
            case 'admin_media_accounts':
            case 'admin_media':
            case 'admin_topups':
            case 'slip_verification':
            case 'admin_packages':
            case 'test_disable_users':
            case 'simple_test':
            case 'test_timezone':
            case 'admin_settings':
            case 'remote_access_control':
                if($is_logged_in && $user['role'] == 'admin') {
                    include 'pages/' . $page . '.php';
                } else {
                    header('Location: ?page=login');
                    exit;
                }
                break;
            default:
                include 'pages/home.php';
                break;
        }
        ?>
    </main>

    <!-- Floating Contact Button -->
    <div class="floating-contact">
        <a href="?page=contact" class="btn btn-success btn-lg rounded-circle shadow-lg"
           data-bs-toggle="tooltip" data-bs-placement="left" title="ติดต่อเรา LINE Open Chat">
            <i class="fab fa-line"></i>
        </a>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5><?php echo $site_name; ?></h5>
                    <p>บริการสตรีมมิ่งหนังและซีรีส์คุณภาพสูง พร้อมให้บริการ 24 ชั่วโมง</p>
                </div>
                <div class="col-md-4 text-center">
                    <h6><i class="fab fa-line me-2"></i>ติดต่อเรา</h6>
                    <p class="mb-2">LINE Open Chat</p>
                    <a href="?page=contact" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-qrcode me-1"></i>ดู QR Code
                    </a>
                </div>
                <div class="col-md-4 text-md-end">
                    <p>&copy; 2024 <?php echo $site_name; ?>. All rights reserved.</p>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>บริการ 24/7
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/animations.js"></script>
    <script>
        // Initialize page-specific animations
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation classes to elements
            const featureCards = document.querySelectorAll('.feature-card');
            featureCards.forEach((card, index) => {
                card.setAttribute('data-animate', 'fade-in');
                card.style.animationDelay = (index * 0.1) + 's';
            });

            // Add counter animation to stats
            const statsNumbers = document.querySelectorAll('.display-4, .h2, .h4');
            statsNumbers.forEach(stat => {
                if (/^\d+/.test(stat.textContent)) {
                    stat.classList.add('counter');
                }
            });

            // Add hover effects to navigation items
            const navItems = document.querySelectorAll('.nav-link');
            navItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add animation to alerts
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.classList.add('slide-in-right');
            });

            // Initialize tooltips for floating contact button
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>
