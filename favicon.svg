<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
    <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#007bff;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#0056b3;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
        </filter>
    </defs>
    
    <!-- Background rounded rectangle with gradient -->
    <rect width="100" height="100" rx="15" fill="url(#grad)" filter="url(#shadow)"/>
    
    <!-- White circle background for play button -->
    <circle cx="50" cy="50" r="25" fill="white" opacity="0.95"/>
    
    <!-- Play triangle -->
    <polygon points="45,35 45,65 70,50" fill="url(#grad)"/>
    
    <!-- Subtle highlight overlay -->
    <rect width="100" height="100" rx="15" fill="url(#highlight)" opacity="0.2"/>
    
    <defs>
        <linearGradient id="highlight" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:white;stop-opacity:0.3" />
            <stop offset="50%" style="stop-color:white;stop-opacity:0" />
            <stop offset="100%" style="stop-color:black;stop-opacity:0.1" />
        </linearGradient>
    </defs>
</svg>
