@echo off
title PayNoi Combined Services
color 0A

echo ========================================
echo   PayNoi Combined Auto Services
echo   Transaction Fetcher and Slip Verification
echo ========================================
echo   Transaction Fetcher (every 5 seconds)
echo   Slip Verification (every 5 seconds)
echo ========================================
echo.

REM Change to project directory
cd /d "%~dp0\.."

echo Current directory: %CD%
echo.

REM Find PHP in various locations
set PHP_PATH=""

REM Try to find PHP in PATH first
php --version >nul 2>&1
if not errorlevel 1 (
    set PHP_PATH=php
    goto php_found
)

REM Try Laragon (specific path from memory)
if exist "C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe" (
    set PHP_PATH="C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe"
    goto php_found
)

REM Try Laragon (auto-detect)
for /d %%i in ("C:\laragon\bin\php\php-*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

REM Try XAMPP
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    goto php_found
)

REM Try WAMP
for /d %%i in ("C:\wamp64\bin\php\php*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

echo ERROR: PHP not found in system
echo Please install PHP or update the path in this script
echo Searched locations:
echo - C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\
echo - C:\laragon\bin\php\
echo - C:\xampp\php\
echo - C:\wamp64\bin\php\
echo - PATH environment
pause
exit /b 1

:php_found
echo SUCCESS: PHP found at: %PHP_PATH%
%PHP_PATH% --version
echo.

REM Check required files
echo Checking required files...
if not exist "cron\continuous_cron.php" (
    echo ERROR: File not found: cron\continuous_cron.php
    pause
    exit /b 1
)
if not exist "cron\slip_verification_cron_5sec.php" (
    echo ERROR: File not found: cron\slip_verification_cron_5sec.php
    pause
    exit /b 1
)
echo SUCCESS: All files are ready
echo.

echo Starting combined services...
echo Logs will be saved in cron/logs/
echo System will run every 5 seconds
echo.
echo Press Ctrl+C to stop
echo ========================================
echo.

REM Create counters for statistics
set /a counter=0
set /a transaction_runs=0
set /a slip_runs=0

:loop
    set /a counter+=1

    REM Show status
    echo [%date% %time%] Round %counter% - Running services...

    REM Run Transaction Fetcher
    echo   Running Transaction Fetcher...
    %PHP_PATH% cron/continuous_cron.php 2>&1 | findstr /C:"SUCCESS" /C:"ERROR" /C:"WARNING" /C:"Found" /C:"Updated"
    if not errorlevel 1 set /a transaction_runs+=1

    REM Run Slip Verification
    echo   Running Slip Verification...
    %PHP_PATH% cron/slip_verification_cron_5sec.php 2>&1 | findstr /C:"SUCCESS" /C:"ERROR" /C:"WARNING" /C:"approved" /C:"verified"
    if not errorlevel 1 set /a slip_runs+=1

    REM Show statistics every 12 rounds (1 minute)
    set /a mod=counter%%12
    if %mod%==0 (
        echo.
        echo STATISTICS: Completed %counter% rounds (1 minute)
        echo    Transaction Fetcher: %transaction_runs% runs
        echo    Slip Verification: %slip_runs% runs
        echo ========================================
    )

    REM Wait 5 seconds
    timeout /t 5 /nobreak >nul

    REM Check if user pressed Ctrl+C
    if errorlevel 1 goto end

goto loop

:end
echo.
echo ========================================
echo STOPPED: Services have been stopped
echo STATISTICS: Total %counter% rounds completed
echo    Transaction Fetcher: %transaction_runs% runs
echo    Slip Verification: %slip_runs% runs
echo Log files: cron\logs\
echo ========================================
pause
