@echo off
title PayNoi Combined Services - Transaction Fetcher & Slip Verification
color 0A

echo ========================================
echo   PayNoi Combined Auto Services
echo   ระบบดึงข้อมูลธุรกรรมและตรวจสอบสลิป
echo ========================================
echo   🔄 Transaction Fetcher (ทุก 5 วินาที)
echo   🔍 Slip Verification (ทุก 5 วินาที)
echo ========================================
echo.

REM เปลี่ยนไปยัง directory ของโปรเจค
cd /d "%~dp0\.."

echo Current directory: %CD%
echo.

REM ค้นหา PHP ในตำแหน่งต่างๆ
set PHP_PATH="C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe"

REM ลองหา PHP ใน PATH ก่อน
php --version >nul 2>&1
if not errorlevel 1 (
    set PHP_PATH=php
    goto php_found
)

REM ลองหาใน Laragon (ตามที่ระบุใน memory)
if exist "C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe" (
    set PHP_PATH="C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe"
    goto php_found
)

REM ลองหาใน Laragon (auto-detect)
for /d %%i in ("C:\laragon\bin\php\php-*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

REM ลองหาใน XAMPP
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    goto php_found
)

REM ลองหาใน WAMP
for /d %%i in ("C:\wamp64\bin\php\php*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

echo ❌ ไม่พบ PHP ในระบบ
echo กรุณาติดตั้ง PHP หรือแก้ไข path ในสคริปนี้
echo ตำแหน่งที่ค้นหา:
echo - C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\
echo - C:\laragon\bin\php\
echo - C:\xampp\php\
echo - C:\wamp64\bin\php\
echo - PATH environment
pause
exit /b 1

:php_found
echo ✅ พบ PHP แล้ว: %PHP_PATH%
%PHP_PATH% --version
echo.

REM ตรวจสอบไฟล์ที่จำเป็น
echo 🔍 ตรวจสอบไฟล์ที่จำเป็น...
if not exist "cron\continuous_cron.php" (
    echo ❌ ไม่พบไฟล์: cron\continuous_cron.php
    pause
    exit /b 1
)
if not exist "cron\slip_verification_cron_5sec.php" (
    echo ❌ ไม่พบไฟล์: cron\slip_verification_cron_5sec.php
    pause
    exit /b 1
)
echo ✅ ไฟล์ทั้งหมดพร้อมใช้งาน
echo.

echo 🚀 เริ่มการทำงานของระบบรวม...
echo 📝 Log จะถูกบันทึกใน cron/logs/
echo 🔄 ระบบจะทำงานทุก 5 วินาที
echo.
echo กด Ctrl+C เพื่อหยุดการทำงาน
echo ========================================
echo.

REM สร้าง counter สำหรับแสดงสถิติ
set /a counter=0
set /a transaction_runs=0
set /a slip_runs=0

:loop
    set /a counter+=1
    
    REM แสดงสถานะ
    echo [%date% %time%] รอบที่ %counter% - กำลังทำงาน...
    
    REM รัน Transaction Fetcher
    echo   🔄 กำลังดึงข้อมูลธุรกรรม...
    %PHP_PATH% cron/continuous_cron.php 2>&1 | findstr /C:"SUCCESS" /C:"ERROR" /C:"WARNING" /C:"Found" /C:"Updated"
    if not errorlevel 1 set /a transaction_runs+=1
    
    REM รัน Slip Verification
    echo   🔍 กำลังตรวจสอบสลิป...
    %PHP_PATH% cron/slip_verification_cron_5sec.php 2>&1 | findstr /C:"อนุมัติ" /C:"ERROR" /C:"WARNING" /C:"สถิติ" /C:"SUCCESS"
    if not errorlevel 1 set /a slip_runs+=1
    
    REM แสดงสถิติทุก 12 รอบ (1 นาที)
    set /a mod=counter%%12
    if %mod%==0 (
        echo.
        echo 📊 สถิติ: ทำงานแล้ว %counter% รอบ (1 นาที)
        echo    🔄 Transaction Fetcher: %transaction_runs% รอบ
        echo    🔍 Slip Verification: %slip_runs% รอบ
        echo ========================================
    )
    
    REM รอ 5 วินาที
    timeout /t 5 /nobreak >nul
    
    REM ตรวจสอบว่าผู้ใช้กด Ctrl+C หรือไม่
    if errorlevel 1 goto end
    
goto loop

:end
echo.
echo ========================================
echo 🛑 หยุดการทำงานแล้ว
echo 📊 รวมทำงาน: %counter% รอบ
echo    🔄 Transaction Fetcher: %transaction_runs% รอบ
echo    🔍 Slip Verification: %slip_runs% รอบ
echo 📝 Log files: cron\logs\
echo ========================================
pause
