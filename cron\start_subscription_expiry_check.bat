@echo off
title PayNoi Subscription Expiry Check - Every 30 Minutes
color 0E

echo ========================================
echo   PayNoi Subscription Expiry Check
echo   ตรวจสอบการหมดอายุทุก 30 นาที
echo ========================================
echo   🔍 Check expired subscriptions
echo   ⚡ Auto-disable expired accounts
echo   🔄 Re-enable renewed subscriptions
echo ========================================
echo.

REM Change to project directory
cd /d "%~dp0\.."

echo Current directory: %CD%
echo.

REM Find PHP in various locations
set PHP_PATH=""

REM Try to find PHP in PATH first
php --version >nul 2>&1
if not errorlevel 1 (
    set PHP_PATH=php
    goto php_found
)

REM Try Laragon (specific path from memory)
if exist "C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe" (
    set PHP_PATH="C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\php.exe"
    goto php_found
)

REM Try Laragon (auto-detect)
for /d %%i in ("C:\laragon\bin\php\php-*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

REM Try XAMPP
if exist "C:\xampp\php\php.exe" (
    set PHP_PATH="C:\xampp\php\php.exe"
    goto php_found
)

REM Try WAMP
for /d %%i in ("C:\wamp64\bin\php\php*") do (
    if exist "%%i\php.exe" (
        set PHP_PATH="%%i\php.exe"
        goto php_found
    )
)

echo ERROR: PHP not found in system
echo Please install PHP or update the path in this script
echo Searched locations:
echo - C:\laragon\bin\php\php-8.3.16-Win32-vs16-x64\
echo - C:\laragon\bin\php\
echo - C:\xampp\php\
echo - C:\wamp64\bin\php\
echo - PATH environment
pause
exit /b 1

:php_found
echo SUCCESS: PHP found at: %PHP_PATH%
%PHP_PATH% --version
echo.

REM Check required files
echo Checking required files...
if not exist "cron\subscription_expiry_check_30min.php" (
    echo ERROR: File not found: cron\subscription_expiry_check_30min.php
    pause
    exit /b 1
)
echo SUCCESS: All files are ready
echo.

echo Starting subscription expiry check service...
echo Logs will be saved in cron/logs/subscription_expiry_30min.log
echo System will run every 30 minutes (1800 seconds)
echo.
echo Press Ctrl+C to stop
echo ========================================
echo.

REM Create counters for statistics
set /a counter=0
set /a successful_runs=0

:loop
    set /a counter+=1
    
    REM Show status
    echo [%date% %time%] Round %counter% - Running subscription expiry check...
    
    REM Run subscription expiry check
    %PHP_PATH% cron/subscription_expiry_check_30min.php 2>&1 | findstr /C:"SUCCESS" /C:"ERROR" /C:"WARNING" /C:"expired" /C:"renewed" /C:"disabled" /C:"enabled"
    if not errorlevel 1 set /a successful_runs+=1
    
    REM Show statistics every 2 hours (4 rounds)
    set /a mod=counter%%4
    if %mod%==0 (
        echo.
        echo STATISTICS: Completed %counter% rounds (2 hours)
        echo    Successful runs: %successful_runs%
        echo    Next check in 30 minutes
        echo ========================================
    )
    
    REM Wait 30 minutes (1800 seconds)
    echo Waiting 30 minutes until next check...
    timeout /t 1800 /nobreak >nul
    
    REM Check if user pressed Ctrl+C
    if errorlevel 1 goto end
    
goto loop

:end
echo.
echo ========================================
echo STOPPED: Subscription expiry check service stopped
echo STATISTICS: Total %counter% rounds completed
echo    Successful runs: %successful_runs%
echo Log files: cron\logs\subscription_expiry_30min.log
echo ========================================
pause
