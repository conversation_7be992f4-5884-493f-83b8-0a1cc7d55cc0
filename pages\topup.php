<?php
require_once 'classes/PaymentManager.php';

// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header('Location: index.php?page=login');
    exit();
}

// Get user data
$database = new Database();
$conn = $database->getConnection();
$query = "SELECT * FROM users WHERE id = :user_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if(!$user) {
    header('Location: index.php?page=login');
    exit();
}

// Handle slip upload
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['upload_slip'])) {
    // Get QR data from POST
    $qr_data_json = $_POST['qr_data'] ?? '';
    $qr_data_array = json_decode($qr_data_json, true);

    if($qr_data_array && isset($_FILES['slip_image']) && $_FILES['slip_image']['error'] == 0) {
        $upload_dir = 'uploads/slips/';
        if(!file_exists($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        $file_extension = strtolower(pathinfo($_FILES['slip_image']['name'], PATHINFO_EXTENSION));
        $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];

        if(in_array($file_extension, $allowed_extensions)) {
            if($_FILES['slip_image']['size'] <= 5 * 1024 * 1024) { // 5MB limit
                $file_name = 'slip_' . $user['id'] . '_' . time() . '.' . $file_extension;
                $file_path = $upload_dir . $file_name;

                if(move_uploaded_file($_FILES['slip_image']['tmp_name'], $file_path)) {
                    // Now create the top-up record with slip
                    $paymentManager = new PaymentManager();
                    $result = $paymentManager->createTopUpWithSlip(
                        $qr_data_array['user_id'],
                        $qr_data_array['unique_amount'],
                        $qr_data_array['qr_url'],
                        $file_path
                    );

                    if($result['success']) {
                        $success_message = 'อัปโหลดสลิปสำเร็จ รอการอนุมัติจากผู้ดูแลระบบ';
                        unset($qr_data); // Clear QR data to show form again
                    } else {
                        $error_message = 'ไม่สามารถบันทึกข้อมูลการเติมเงินได้: ' . $result['message'];
                        // Delete uploaded file if database save failed
                        if(file_exists($file_path)) {
                            unlink($file_path);
                        }
                    }
                } else {
                    $error_message = "เกิดข้อผิดพลาดในการอัปโหลดไฟล์";
                }
            } else {
                $error_message = 'ขนาดไฟล์ใหญ่เกินไป (สูงสุด 5MB)';
            }
        } else {
            $error_message = "รองรับเฉพาะไฟล์ JPG, JPEG, PNG, GIF เท่านั้น";
        }
    } else {
        $error_message = "กรุณาเลือกไฟล์รูปภาพ";
    }
}

// Handle top-up request
if($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['generate_qr'])) {
    $amount = floatval($_POST['amount'] ?? 0);

    if($amount >= 10 && $amount <= 10000) {
        $paymentManager = new PaymentManager();
        $result = $paymentManager->generatePromptPayQR($amount, $user['id']);

        if($result['success']) {
            $qr_data = $result;
            $success_message = "สร้าง QR Code สำเร็จ" . (isset($result['service']) ? " (ใช้บริการ: {$result['service']})" : "");
        } else {
            $error_message = $result['message'];
        }
    } else {
        $error_message = 'จำนวนเงินต้องอยู่ระหว่าง 10 - 10,000 บาท';
    }
}

// Get user's top-up history
$paymentManager = new PaymentManager();
$topup_history = $paymentManager->getUserTopUps($user['id']);
?>

<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="fw-bold">เติมเงิน</h2>
            <p class="text-muted">เติมเงินเข้าบัญชีเพื่อซื้อแพ็คเกจบริการ</p>
        </div>
    </div>
    
    <!-- Current Balance -->
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-info">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="fas fa-wallet me-2"></i>
                        <strong>ยอดเงินคงเหลือ: <?php
                            $balance = floatval($user['balance']);
                            echo number_format($balance, 0, '.', ',') . ' ฿';
                        ?></strong>
                    </div>
                    <a href="?page=packages" class="btn btn-primary btn-sm">
                        <i class="fas fa-shopping-cart me-1"></i>ซื้อแพ็คเกจ
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Messages -->
    <?php if(isset($error_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if(isset($success_message)): ?>
    <div class="row mb-4">
        <div class="col">
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>







    <div class="row">
        <!-- Top-up Form -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-qrcode me-2"></i>เติมเงินด้วย PromptPay</h5>
                </div>
                <div class="card-body">
                    <?php if(!isset($qr_data)): ?>
                    <form method="POST">
                        <div class="mb-3">
                            <label for="amount" class="form-label">จำนวนเงิน (บาท)</label>
                            <div class="input-group">
                                <span class="input-group-text">฿</span>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       min="10" max="10000" step="0.01" required
                                       value="<?php echo htmlspecialchars($_POST['amount'] ?? ''); ?>">
                            </div>
                            <div class="form-text">ขั้นต่ำ 10 บาท สูงสุด 10,000 บาท</div>
                        </div>
                        
                        <!-- Quick Amount Buttons -->
                        <div class="mb-3">
                            <label class="form-label">จำนวนเงินยอดนิยม</label>
                            <div class="d-grid gap-2">
                                <div class="row g-2">
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="100">100 ฿</button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="200">200 ฿</button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="500">500 ฿</button>
                                    </div>
                                </div>
                                <div class="row g-2">
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="1000">1,000 ฿</button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="2000">2,000 ฿</button>
                                    </div>
                                    <div class="col-4">
                                        <button type="button" class="btn btn-outline-primary w-100 quick-amount" data-amount="5000">5,000 ฿</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <input type="hidden" name="generate_qr" value="1">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-qrcode me-2"></i>สร้าง QR Code
                            </button>
                        </div>
                    </form>
                    <?php else: ?>
                    <!-- QR Code Display -->
                    <div class="qr-code-container">
                        <h6 class="text-center mb-3 fade-in">สแกน QR Code เพื่อชำระเงิน</h6>
                        <img src="<?php echo htmlspecialchars($qr_data['qr_url']); ?>"
                             alt="PromptPay QR Code" class="img-fluid mx-auto d-block mb-3 qr-code-animation"
                             style="max-width: 300px;">

                        <div class="text-center">
                            <h5 class="text-primary counter bounce-in">จำนวนเงิน: <?php echo number_format(floatval($qr_data['amount']), 2, '.', ','); ?> ฿</h5>
                            <?php if(isset($qr_data['original_amount'])): ?>
                            <p class="text-info small mb-2">
                                <i class="fas fa-info-circle me-1"></i>
                                จำนวนเดิม: <?php echo number_format($qr_data['original_amount'], 0, '.', ','); ?> ฿
                                (เพิ่มทศนิยมเพื่อการจับคู่ที่แม่นยำ)
                            </p>
                            <?php endif; ?>
                            <p class="text-muted mb-3">
                                <i class="fas fa-phone me-1"></i>
                                PromptPay: <?php echo htmlspecialchars($qr_data['phone']); ?>
                            </p>
                            
                            <!-- Upload Slip Form -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-upload me-2"></i>แนบสลิปการโอนเงิน</h6>
                                </div>
                                <div class="card-body">
                                    <form method="POST" enctype="multipart/form-data">
                                        <input type="hidden" name="qr_data" value="<?php echo htmlspecialchars(json_encode($qr_data)); ?>">
                                        <input type="hidden" name="upload_slip" value="1">

                                        <div class="mb-3">
                                            <label for="slip_image" class="form-label">
                                                <i class="fas fa-image me-2"></i>เลือกรูปสลิป
                                            </label>
                                            <input type="file"
                                                   class="form-control"
                                                   id="slip_image"
                                                   name="slip_image"
                                                   accept="image/*"
                                                   required>
                                            <div class="form-text">
                                                รองรับไฟล์: JPG, JPEG, PNG, GIF (ขนาดไม่เกิน 5MB)
                                            </div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-upload me-2"></i>อัปโหลดสลิป
                                            </button>
                                            <a href="?page=topup" class="btn btn-secondary">
                                                <i class="fas fa-arrow-left me-1"></i>เติมเงินใหม่
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    หลังจากแนบสลิปแล้ว กรุณารอการอนุมัติจากผู้ดูแลระบบ (ประมาณ 5-10 นาที)
                                </small>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Payment Instructions -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>วิธีการชำระเงิน</h6>
                </div>
                <div class="card-body">
                    <ol class="mb-3">
                        <li>กรอกจำนวนเงินที่ต้องการเติม</li>
                        <li>กดปุ่ม "สร้าง QR Code"</li>
                        <li>สแกน QR Code ด้วยแอปธนาคารของคุณ</li>
                        <li>ยืนยันการชำระเงิน <strong>ตามจำนวนที่แสดงใน QR Code</strong></li>
                        <li><strong>อัปโหลดสลิปการโอนเงิน</strong></li>
                        <li>ระบบจะตรวจสอบและอนุมัติอัตโนมัติ</li>
                    </ol>

                    <div class="alert alert-warning small mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>สำคัญ:</strong> ข้อมูลการเติมเงินจะถูกบันทึกเฉพาะเมื่ออัปโหลดสลิปแล้วเท่านั้น
                        ระบบจะเพิ่มทศนิยม 2 ตำแหน่งให้กับจำนวนเงินเพื่อการจับคู่ที่แม่นยำ
                        กรุณาโอนเงินตามจำนวนที่แสดงใน QR Code และอัปโหลดสลิปทันที
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Top-up History -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>ประวัติการเติมเงิน</h5>
                </div>
                <div class="card-body">
                    <?php if(!empty($topup_history)): ?>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>วันที่</th>
                                    <th>จำนวน</th>
                                    <th>สลิป</th>
                                    <th>สถานะ</th>
                                    <th>หมายเหตุ</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($topup_history as $topup): ?>
                                <tr>
                                    <td>
                                        <small><?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?></small>
                                    </td>
                                    <td>
                                        <strong><?php echo number_format($topup['amount'], 2); ?> ฿</strong>
                                    </td>
                                    <td>
                                        <?php if($topup['slip_image']): ?>
                                        <a href="<?php echo htmlspecialchars($topup['slip_image']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-image"></i>
                                        </a>
                                        <?php else: ?>
                                        <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge <?php
                                            echo $topup['status'] == 'approved' ? 'bg-success' :
                                                ($topup['status'] == 'pending' ? 'bg-warning' : 'bg-danger');
                                        ?>">
                                            <?php
                                            echo $topup['status'] == 'approved' ? 'อนุมัติแล้ว' :
                                                ($topup['status'] == 'pending' ? 'รอดำเนินการ' : 'ปฏิเสธ');
                                            ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php if ($topup['status'] == 'approved' && $topup['trans_id']): ?>
                                            <?php if ($topup['is_duplicate_slip']): ?>
                                                <span class="badge bg-warning text-dark" title="สลิปนี้ถูกใช้กับการเติมเงินอื่นแล้ว">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>สลิปซ้ำ
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">
                                                    <i class="fas fa-check-circle me-1"></i>ปกติ
                                                </span>
                                            <?php endif; ?>
                                        <?php elseif ($topup['status'] == 'approved'): ?>
                                            <span class="text-muted">
                                                <i class="fas fa-user me-1"></i>อนุมัติด้วยตนเอง
                                            </span>
                                        <?php elseif ($topup['status'] == 'rejected'): ?>
                                            <?php
                                            $slip_data = json_decode($topup['slip_data'], true);
                                            if ($slip_data && isset($slip_data['rejection_reason'])):
                                                if (strpos($slip_data['rejection_reason'], 'สลิปซ้ำ') !== false):
                                            ?>
                                                    <span class="badge bg-danger" title="<?php echo htmlspecialchars($slip_data['rejection_reason']); ?>">
                                                        <i class="fas fa-times-circle me-1"></i>สลิปซ้ำ - ปฏิเสธ
                                                    </span>
                                            <?php elseif (strpos($slip_data['rejection_reason'], 'สลิปผิด') !== false): ?>
                                                    <span class="badge bg-danger" title="<?php echo htmlspecialchars($slip_data['rejection_reason']); ?>">
                                                        <i class="fas fa-exclamation-triangle me-1"></i>สลิปผิด - ปฏิเสธ
                                                    </span>
                                            <?php else: ?>
                                                    <span class="badge bg-danger" title="<?php echo htmlspecialchars($slip_data['rejection_reason']); ?>">
                                                        <i class="fas fa-times-circle me-1"></i>ปฏิเสธ
                                                    </span>
                                            <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times-circle me-1"></i>ปฏิเสธ
                                                </span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-history text-muted" style="font-size: 3rem;"></i>
                        <h6 class="mt-3 text-muted">ยังไม่มีประวัติการเติมเงิน</h6>
                        <p class="text-muted">เริ่มเติมเงินเพื่อซื้อแพ็คเกจบริการ</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Pending Top-ups -->
            <?php
            $pending_topups = array_filter($topup_history, function($topup) {
                return $topup['status'] == 'pending';
            });
            ?>
            
            <?php if(!empty($pending_topups)): ?>
            <div class="card mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-clock me-2"></i>รายการรอดำเนินการ
                    </h6>
                </div>
                <div class="card-body">
                    <?php foreach($pending_topups as $topup): ?>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong><?php echo number_format($topup['amount'], 2); ?> ฿</strong>
                            <br>
                            <small class="text-muted">
                                <?php echo date('d/m/Y H:i', strtotime($topup['created_at'])); ?>
                            </small>
                        </div>
                        <span class="badge bg-warning">รอดำเนินการ</span>
                    </div>
                    <?php endforeach; ?>
                    
                    <div class="alert alert-info mt-3 mb-0">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            กรุณารอการอนุมัติจากผู้ดูแลระบบ หากมีปัญหาติดต่อ Admin
                        </small>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Quick amount buttons
document.querySelectorAll('.quick-amount').forEach(button => {
    button.addEventListener('click', function() {
        const amount = this.getAttribute('data-amount');
        document.getElementById('amount').value = amount;
        
        // Remove active class from all buttons
        document.querySelectorAll('.quick-amount').forEach(btn => {
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
        });
        
        // Add active class to clicked button
        this.classList.remove('btn-outline-primary');
        this.classList.add('btn-primary');
    });
});
</script>
