# Subscription Expiry Check System

ระบบตรวจสอบการหมดอายุของ subscription อัตโนมัติ

## 📋 Overview

ระบบนี้จะตรวจสอบ subscription ที่หมดอายุและดำเนินการดังนี้:
- เปลี่ยนสถานะ `user_subscriptions.status` เป็น `expired`
- ปิดการเข้าถึง media server (Emby/Jellyfin) สำหรับ user ที่หมดอายุ
- เปิดการเข้าถึงใหม่สำหรับ user ที่ต่ออายุแล้ว

## 🚀 การใช้งาน

### 1. รันแบบ Standalone (ทุก 30 นาที)
```bash
# Windows
cron\start_subscription_expiry_check.bat

# หรือรันครั้งเดียว
php cron\subscription_expiry_check_30min.php
```

### 2. รันแบบ Combined Services (แนะนำ)
```bash
# รวมทุกระบบในไฟล์เดียว
cron\run_combined_services.bat
```

## ⚙️ การทำงาน

### ระบบตรวจสอบ:
1. **Expired Subscriptions**: subscription ที่ `status = 'active'` แต่ `end_date < CURDATE()`
2. **Renewed Subscriptions**: subscription ที่ `status = 'active'` และ `end_date >= CURDATE()` แต่ media server accounts ยัง disabled

### การดำเนินการ:
1. **เมื่อหมดอายุ**:
   - เปลี่ยน `user_subscriptions.status` เป็น `expired`
   - เปลี่ยน `emby_accounts.status` เป็น `inactive`
   - เปลี่ยน `jellyfin_accounts.status` เป็น `inactive`
   - ปิด remote access ใน media server (IsDisabled=true)

2. **เมื่อต่ออายุ**:
   - เปลี่ยน `emby_accounts.status` เป็น `active`
   - เปลี่ยน `jellyfin_accounts.status` เป็น `active`
   - เปิด remote access ใน media server (IsDisabled=false)

## 📊 Log และ Monitoring

### Log Files:
- **Standalone**: `cron/logs/subscription_expiry_30min.log`
- **Combined**: แสดงใน console และ log files ของแต่ละระบบ

### สถิติที่แสดง:
- จำนวน subscription ที่ active (valid)
- จำนวน subscription ที่ active แต่หมดอายุแล้ว
- จำนวน subscription ที่ถูกทำเครื่องหมาย expired แล้ว
- ผลการดำเนินการแต่ละ user

## 🔧 การตั้งค่า

### ความถี่การทำงาน:
- **Standalone**: ทุก 30 นาที (1800 วินาที)
- **Combined**: ทุก 30 นาที (360 รอบ × 5 วินาที)

### การปรับแต่ง:
แก้ไขไฟล์ `cron/subscription_expiry_check_30min.php`:
```php
// เปลี่ยนช่วงเวลาการตรวจสอบ
$query = "SELECT us.*, u.username, u.id as user_id 
          FROM user_subscriptions us 
          JOIN users u ON us.user_id = u.id 
          WHERE us.status = 'active' AND us.end_date < CURDATE()";
```

## 📝 ตัวอย่าง Log

```
[2025-06-23 21:15:57] [START] === Starting subscription expiry check (30min interval) ===
[2025-06-23 21:15:57] [SUCCESS] ✅ Database connection successful
[2025-06-23 21:15:57] [INFO] 📊 Current subscription status:
[2025-06-23 21:15:57] [INFO]    - Active (valid): 3
[2025-06-23 21:15:57] [INFO]    - Active (expired): 0
[2025-06-23 21:15:57] [INFO]    - Already marked expired: 1
[2025-06-23 21:15:57] [INFO] ℹ️ No expired subscriptions found - all subscriptions are up to date
[2025-06-23 21:15:57] [END] === Subscription expiry check completed successfully ===
```

## 🛠️ Troubleshooting

### ปัญหาที่อาจเกิดขึ้น:

1. **Database Connection Error**:
   - ตรวจสอบการตั้งค่าฐานข้อมูลใน `config/db_config.php`

2. **Media Server API Error**:
   - ตรวจสอบ API keys ใน admin settings
   - ตรวจสอบการเชื่อมต่อ media server

3. **Permission Error**:
   - ตรวจสอบสิทธิ์การเขียนไฟล์ log
   - ตรวจสอบสิทธิ์การเข้าถึงฐานข้อมูล

### การตรวจสอบ Manual:
```php
// ตรวจสอบ subscription ที่หมดอายุ
$subscriptionManager = new SubscriptionManager();
$result = $subscriptionManager->checkExpiredSubscriptions();
print_r($result);
```

## 🔗 Integration

ระบบนี้ทำงานร่วมกับ:
- **Transaction Fetcher**: ดึงข้อมูลธุรกรรม
- **Slip Verification**: ตรวจสอบสลิป
- **Media Server APIs**: จัดการ user accounts

ใช้ `run_combined_services.bat` เพื่อรันทุกระบบพร้อมกัน
