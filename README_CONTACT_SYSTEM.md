# LINE Open Chat Contact System

ระบบติดต่อผ่าน LINE Open Chat พร้อม QR Code สำหรับเว็บไซต์

## 📋 Overview

ระบบติดต่อที่เพิ่มเข้ามาประกอบด้วย:
- หน้าติดต่อแยก (Contact Page)
- ปุ่มลอยสำหรับติดต่อ (Floating Contact Button)
- ลิงก์ติดต่อใน Footer และ Navigation
- QR Code สำหรับ LINE Open Chat

## 🎯 คุณสมบัติ

### 1. หน้าติดต่อ (`pages/contact.php`)
- **QR Code Display**: แสดง QR Code จาก `pictures/lineopen.jpg`
- **Contact Information**: ข้อมูลการติดต่อและเวลาให้บริการ
- **FAQ Section**: คำถามที่พบบ่อย
- **Responsive Design**: รองรับทุกขนาดหน้าจอ

### 2. Floating Contact Button
- **ตำแหน่ง**: มุมขวาล่างของหน้าจอ
- **Animation**: เอฟเฟกต์ลอยและ hover
- **Responsive**: ปรับขนาดตามหน้าจอ
- **Tooltip**: แสดงข้อความเมื่อ hover

### 3. Navigation Integration
- **เมนูหลัก**: เพิ่มลิงก์ "ติดต่อเรา" ในเมนู
- **Footer**: ส่วนติดต่อใน footer
- **Homepage**: ปุ่มติดต่อในหน้าแรก (สำหรับผู้ที่ยังไม่ login)

## 🎨 Design Features

### Visual Elements:
- **LINE Brand Colors**: ใช้สีเขียวของ LINE
- **Gradient Effects**: เอฟเฟกต์ไล่สีสวยงาม
- **Shadow Effects**: เงาและ depth
- **Smooth Animations**: การเคลื่อนไหวที่นุ่มนวล

### Responsive Design:
- **Desktop**: ปุ่มขนาด 60x60px
- **Mobile**: ปุ่มขนาด 50x50px
- **QR Code**: ปรับขนาดตามหน้าจอ

## 📁 ไฟล์ที่เกี่ยวข้อง

```
├── pages/
│   └── contact.php              # หน้าติดต่อหลัก
├── pictures/
│   └── lineopen.jpg            # QR Code สำหรับ LINE Open Chat
├── index.php                   # เพิ่ม navigation และ floating button
└── README_CONTACT_SYSTEM.md    # เอกสารนี้
```

## 🔧 การติดตั้งและใช้งาน

### 1. เตรียม QR Code:
```bash
# วาง QR Code ไฟล์ในโฟลเดอร์ pictures/
pictures/lineopen.jpg
```

### 2. การเข้าถึง:
- **URL**: `?page=contact`
- **Navigation**: คลิก "ติดต่อเรา" ในเมนู
- **Floating Button**: คลิกปุ่มลอยมุมขวาล่าง
- **Footer**: คลิก "ดู QR Code" ใน footer

### 3. การปรับแต่ง:

#### เปลี่ยน QR Code:
```php
// ใน pages/contact.php
<img src="pictures/lineopen.jpg" alt="LINE Open Chat QR Code">
```

#### ปรับสีปุ่มลอย:
```css
/* ใน index.php */
.floating-contact .btn {
    background: linear-gradient(45deg, #00c851, #00ff41);
}
```

#### เปลี่ยนตำแหน่งปุ่มลอย:
```css
.floating-contact {
    bottom: 30px;  /* ระยะจากด้านล่าง */
    right: 30px;   /* ระยะจากด้านขวา */
}
```

## 📱 การใช้งานสำหรับผู้ใช้

### วิธีติดต่อ:
1. **เปิดหน้าติดต่อ**: คลิกเมนู "ติดต่อเรา" หรือปุ่มลอย
2. **สแกน QR Code**: ใช้แอป LINE สแกน QR Code
3. **เข้าร่วม Open Chat**: กดเข้าร่วมแชทกลุ่ม
4. **ส่งข้อความ**: เริ่มสนทนากับทีมงาน

### เรื่องที่สามารถติดต่อได้:
- การสมัครสมาชิก
- การชำระเงิน
- ปัญหาการใช้งาน
- แพ็คเกจและราคา
- ข้อสงสัยทั่วไป

## 🎯 ข้อดีของระบบ

### สำหรับผู้ใช้:
- **สะดวก**: ติดต่อผ่าน LINE ที่คุ้นเคย
- **รวดเร็ว**: ตอบกลับภายใน 5-10 นาที
- **24/7**: บริการตลอดเวลา
- **ส่งไฟล์ได้**: สามารถส่งรูปภาพ, เอกสาร

### สำหรับเจ้าของเว็บ:
- **จัดการง่าย**: ใช้ LINE Open Chat
- **ไม่เสียค่าใช้จ่าย**: ฟรี
- **ติดตามได้**: เห็นประวัติการสนทนา
- **หลายคนดูแลได้**: เพิ่ม admin หลายคน

## 🔄 การอัปเดต

### เปลี่ยน QR Code ใหม่:
1. สร้าง LINE Open Chat ใหม่
2. บันทึก QR Code เป็น `pictures/lineopen.jpg`
3. ไฟล์จะอัปเดตอัตโนมัติ

### เพิ่มช่องทางติดต่ออื่น:
```php
// เพิ่มใน pages/contact.php
<div class="contact-item mb-4">
    <div class="d-flex align-items-center mb-3">
        <div class="icon-circle bg-primary text-white me-3">
            <i class="fas fa-envelope"></i>
        </div>
        <div>
            <h5 class="mb-1">Email</h5>
            <p class="text-muted mb-0"><EMAIL></p>
        </div>
    </div>
</div>
```

## 📊 Analytics และ Tracking

### การติดตาม:
- จำนวนคลิกปุ่มติดต่อ
- การเข้าชมหน้าติดต่อ
- อัตราการแปลงจากการติดต่อ

### Google Analytics Event:
```javascript
// เพิ่มใน index.php
document.querySelector('.floating-contact a').addEventListener('click', function() {
    gtag('event', 'contact_click', {
        'event_category': 'engagement',
        'event_label': 'floating_button'
    });
});
```

## 🛠️ Troubleshooting

### ปัญหาที่อาจเกิดขึ้น:

1. **QR Code ไม่แสดง**:
   - ตรวจสอบไฟล์ `pictures/lineopen.jpg`
   - ตรวจสอบ permissions ของโฟลเดอร์

2. **ปุ่มลอยไม่แสดง**:
   - ตรวจสอบ CSS ใน `index.php`
   - ตรวจสอบ z-index conflicts

3. **Tooltip ไม่ทำงาน**:
   - ตรวจสอบ Bootstrap JavaScript
   - ตรวจสอบ tooltip initialization

### การแก้ไข:
```bash
# ตรวจสอบไฟล์
ls -la pictures/lineopen.jpg

# ตรวจสอบ permissions
chmod 644 pictures/lineopen.jpg
```

ระบบติดต่อ LINE Open Chat พร้อมใช้งานแล้ว! 🎉
